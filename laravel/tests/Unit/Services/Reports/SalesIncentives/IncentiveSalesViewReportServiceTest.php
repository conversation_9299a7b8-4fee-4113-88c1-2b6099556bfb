<?php

namespace Tests\Unit\Services\Reports\SalesIncentives;

use App\Brand;
use App\Line;
use App\LineDivision;
use App\Product;
use App\Services\Reports\SalesIncentives\IncentiveSalesViewReportService;
use App\Services\Sales\SalesIncentiveHolder;
use Illuminate\Support\Collection;
use Mockery;
use PHPUnit\Framework\TestCase;

class IncentiveSalesViewReportServiceTest extends TestCase
{
    private IncentiveSalesViewReportService $service;
    private SalesIncentiveHolder $mockSalesIncentiveHolder;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockSalesIncentiveHolder = Mockery::mock(SalesIncentiveHolder::class);
        $this->service = new IncentiveSalesViewReportService($this->mockSalesIncentiveHolder);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_group_products_by_brand_groups_correctly()
    {
        // Arrange
        $sampleData = collect([
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product A',
                'p_w' => 10.5,
                'product_value' => 100.0,
                'brand' => 'Brand X',
                'sales_unit' => 50,
                'sales_value' => 500.0,
                'target_unit' => 40,
                'target_value' => 400.0,
                'achievement_unit' => '125.00%',
                'achievement_value' => '125.00%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
            ],
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product B',
                'p_w' => 15.5,
                'product_value' => 150.0,
                'brand' => 'Brand X',
                'sales_unit' => 30,
                'sales_value' => 300.0,
                'target_unit' => 25,
                'target_value' => 250.0,
                'achievement_unit' => '120.00%',
                'achievement_value' => '120.00%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
            ],
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product C',
                'p_w' => 8.0,
                'product_value' => 80.0,
                'brand' => '', // No brand
                'sales_unit' => 20,
                'sales_value' => 200.0,
                'target_unit' => 30,
                'target_value' => 300.0,
                'achievement_unit' => '66.67%',
                'achievement_value' => '66.67%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
            ],
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Total',
                'p_w' => '',
                'product_value' => 330.0,
                'brand' => '',
                'sales_unit' => 100,
                'sales_value' => 1000.0,
                'target_unit' => 95,
                'target_value' => 950.0,
                'achievement_unit' => '105.26%',
                'achievement_value' => '105.26%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => true,
            ],
        ]);

        // Act
        $result = $this->service->groupProductsByBrand($sampleData);

        // Assert
        $this->assertCount(3, $result); // 2 brand groups + 1 total
        
        // Check Brand X group
        $brandXGroup = $result->where('product', 'Brand X')->first();
        $this->assertNotNull($brandXGroup);
        $this->assertEquals('Brand X', $brandXGroup['brand']);
        $this->assertEquals(26.0, $brandXGroup['p_w']); // 10.5 + 15.5
        $this->assertEquals(250.0, $brandXGroup['product_value']); // 100.0 + 150.0
        $this->assertEquals(80, $brandXGroup['sales_unit']); // 50 + 30
        $this->assertEquals(800.0, $brandXGroup['sales_value']); // 500.0 + 300.0
        $this->assertEquals(65, $brandXGroup['target_unit']); // 40 + 25
        $this->assertEquals(650.0, $brandXGroup['target_value']); // 400.0 + 250.0
        $this->assertEquals('123.08%', $brandXGroup['achievement_unit']); // 80/65*100
        $this->assertEquals('123.08%', $brandXGroup['achievement_value']); // 800/650*100
        $this->assertEquals(2, $brandXGroup['product_count']);
        $this->assertFalse($brandXGroup['is_total']);

        // Check No Brand group
        $noBrandGroup = $result->where('product', 'No Brand')->first();
        $this->assertNotNull($noBrandGroup);
        $this->assertEquals('', $noBrandGroup['brand']);
        $this->assertEquals(8.0, $noBrandGroup['p_w']);
        $this->assertEquals(80.0, $noBrandGroup['product_value']);
        $this->assertEquals(20, $noBrandGroup['sales_unit']);
        $this->assertEquals(200.0, $noBrandGroup['sales_value']);
        $this->assertEquals(1, $noBrandGroup['product_count']);
        $this->assertFalse($noBrandGroup['is_total']);

        // Check total record is preserved
        $totalRecord = $result->where('is_total', true)->first();
        $this->assertNotNull($totalRecord);
        $this->assertEquals('Total', $totalRecord['product']);
        $this->assertTrue($totalRecord['is_total']);
    }

    public function test_calculate_achievement_percentage_handles_zero_target()
    {
        // Arrange
        $sampleData = collect([
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product A',
                'p_w' => 10.0,
                'product_value' => 100.0,
                'brand' => 'Brand X',
                'sales_unit' => 50,
                'sales_value' => 500.0,
                'target_unit' => 0, // Zero target
                'target_value' => 0, // Zero target
                'achievement_unit' => '0%',
                'achievement_value' => '0%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
            ],
        ]);

        // Act
        $result = $this->service->groupProductsByBrand($sampleData);

        // Assert
        $brandGroup = $result->first();
        $this->assertEquals('0%', $brandGroup['achievement_unit']);
        $this->assertEquals('0%', $brandGroup['achievement_value']);
    }
}
