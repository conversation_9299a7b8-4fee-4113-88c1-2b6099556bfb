<?php

/**
 * Example demonstrating the before/after data structure when using brand grouping
 * in IncentiveSalesViewReportService
 */

// BEFORE: Individual Product Records
$individualProductData = [
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => '<PERSON>',
        'emp_code' => 'EMP001',
        'product' => 'Aspirin 100mg',
        'p_w' => 15.5,
        'product_value' => 120.0,
        'brand' => 'CardioMax',
        'sales_unit' => 100,
        'sales_value' => 1500.0,
        'target_unit' => 80,
        'target_value' => 1200.0,
        'achievement_unit' => '125.00%',
        'achievement_value' => '125.00%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
    ],
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => '<PERSON>',
        'emp_code' => 'EMP001',
        'product' => 'Clopidogrel 75mg',
        'p_w' => 25.0,
        'product_value' => 200.0,
        'brand' => 'CardioMax',
        'sales_unit' => 60,
        'sales_value' => 1800.0,
        'target_unit' => 50,
        'target_value' => 1500.0,
        'achievement_unit' => '120.00%',
        'achievement_value' => '120.00%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
    ],
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Generic Statin',
        'p_w' => 8.0,
        'product_value' => 60.0,
        'brand' => '', // No brand
        'sales_unit' => 40,
        'sales_value' => 600.0,
        'target_unit' => 45,
        'target_value' => 675.0,
        'achievement_unit' => '88.89%',
        'achievement_value' => '88.89%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
    ],
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Atorvastatin 20mg',
        'p_w' => 18.5,
        'product_value' => 150.0,
        'brand' => 'LipidCare',
        'sales_unit' => 75,
        'sales_value' => 1125.0,
        'target_unit' => 70,
        'target_value' => 1050.0,
        'achievement_unit' => '107.14%',
        'achievement_value' => '107.14%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
    ],
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Total',
        'p_w' => '',
        'product_value' => 530.0,
        'brand' => '',
        'sales_unit' => 275,
        'sales_value' => 5025.0,
        'target_unit' => 245,
        'target_value' => 4425.0,
        'achievement_unit' => '112.24%',
        'achievement_value' => '113.56%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => true,
    ],
];

// AFTER: Hierarchical Brand Grouped Data
$brandGroupedData = [
    // CardioMax Brand Summary
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'CardioMax', // Brand name becomes product name
        'p_w' => 40.5, // 15.5 + 25.0
        'product_value' => 320.0, // 120.0 + 200.0
        'brand' => 'CardioMax',
        'sales_unit' => 160, // 100 + 60
        'sales_value' => 3300.0, // 1500.0 + 1800.0
        'target_unit' => 130, // 80 + 50
        'target_value' => 2700.0, // 1200.0 + 1500.0
        'achievement_unit' => '123.08%', // (160/130)*100
        'achievement_value' => '122.22%', // (3300/2700)*100
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => true, // NEW: Brand summary record
        'product_count' => 2, // NEW: Shows 2 products were grouped
    ],
    // CardioMax Individual Product 1
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Aspirin 100mg', // Original product name
        'p_w' => 15.5,
        'product_value' => 120.0,
        'brand' => 'CardioMax',
        'sales_unit' => 100,
        'sales_value' => 1500.0,
        'target_unit' => 80,
        'target_value' => 1200.0,
        'achievement_unit' => '125.00%',
        'achievement_value' => '125.00%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => false, // Individual product
    ],
    // CardioMax Individual Product 2
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Clopidogrel 75mg', // Original product name
        'p_w' => 25.0,
        'product_value' => 200.0,
        'brand' => 'CardioMax',
        'sales_unit' => 60,
        'sales_value' => 1800.0,
        'target_unit' => 50,
        'target_value' => 1500.0,
        'achievement_unit' => '120.00%',
        'achievement_value' => '120.00%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => false, // Individual product
    ],
    // LipidCare Brand Summary
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'LipidCare', // Brand name becomes product name
        'p_w' => 18.5,
        'product_value' => 150.0,
        'brand' => 'LipidCare',
        'sales_unit' => 75,
        'sales_value' => 1125.0,
        'target_unit' => 70,
        'target_value' => 1050.0,
        'achievement_unit' => '107.14%',
        'achievement_value' => '107.14%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => true, // Brand summary record
        'product_count' => 1, // Only one product in this brand
    ],
    // LipidCare Individual Product
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Atorvastatin 20mg', // Original product name
        'p_w' => 18.5,
        'product_value' => 150.0,
        'brand' => 'LipidCare',
        'sales_unit' => 75,
        'sales_value' => 1125.0,
        'target_unit' => 70,
        'target_value' => 1050.0,
        'achievement_unit' => '107.14%',
        'achievement_value' => '107.14%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => false, // Individual product
    ],
    // No Brand Summary
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'No Brand', // Special handling for products without brand
        'p_w' => 8.0,
        'product_value' => 60.0,
        'brand' => '', // Empty brand field for no-brand products
        'sales_unit' => 40,
        'sales_value' => 600.0,
        'target_unit' => 45,
        'target_value' => 675.0,
        'achievement_unit' => '88.89%',
        'achievement_value' => '88.89%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => true, // Brand summary record
        'product_count' => 1,
    ],
    // No Brand Individual Product
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Generic Statin', // Original product name
        'p_w' => 8.0,
        'product_value' => 60.0,
        'brand' => '', // No brand
        'sales_unit' => 40,
        'sales_value' => 600.0,
        'target_unit' => 45,
        'target_value' => 675.0,
        'achievement_unit' => '88.89%',
        'achievement_value' => '88.89%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => false,
        'is_brand_summary' => false, // Individual product
    ],
    // Total Record (unchanged)
    [
        'id' => 1,
        'line' => 'Cardiology Line',
        'division' => 'District A',
        'employee' => 'John Smith',
        'emp_code' => 'EMP001',
        'product' => 'Total',
        'p_w' => '',
        'product_value' => 530.0,
        'brand' => '',
        'sales_unit' => 275,
        'sales_value' => 5025.0,
        'target_unit' => 245,
        'target_value' => 4425.0,
        'achievement_unit' => '112.24%',
        'achievement_value' => '113.56%',
        'color' => '#FF5733',
        'level' => 3,
        'is_total' => true, // Total records are preserved unchanged
        'is_brand_summary' => false,
    ],
];

/**
 * Key Changes Summary:
 *
 * 1. HIERARCHICAL STRUCTURE: 4 individual products → 3 brand summaries + 4 individual products + 1 total (8 records total)
 * 2. BRAND SUMMARIES: Each brand gets a summary record with aggregated data
 * 3. PRESERVED PRODUCTS: All individual products are preserved after their brand summary
 * 4. AGGREGATION: Brand summaries combine data from multiple products (CardioMax = Aspirin + Clopidogrel)
 * 5. SINGLE PRODUCTS: Even single-product brands get a summary (LipidCare, No Brand)
 * 6. NO BRAND HANDLING: Products without brands are grouped under "No Brand" summary
 * 7. NEW FIELDS:
 *    - is_brand_summary: true for brand summaries, false for individual products
 *    - product_count: shows how many products were grouped in each brand
 * 8. RECALCULATED: Achievement percentages in brand summaries based on aggregated totals
 * 9. PRESERVED: Total record and all individual product records remain exactly the same
 * 10. STRUCTURE: [Brand Summary] → [Product 1] → [Product 2] → [Next Brand Summary] → [Product 3] → [Total]
 */

// Usage example:
/*
$service = new IncentiveSalesViewReportService($salesIncentiveHolder);

// Get individual products (original behavior)
$individual = $service->salesView($division, $line, $products, false);

// Get brand-grouped results (new feature)
$grouped = $service->salesView($division, $line, $products, true);

// Or manually group existing results
$grouped = $service->groupProductsByBrand($individual);
*/
