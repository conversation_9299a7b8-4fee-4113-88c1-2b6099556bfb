<?php

/**
 * Exact Structure Example - Shows the hierarchical brand grouping structure
 * as requested: Brand summary followed by individual products
 */

// Example of the exact structure you requested:
// [brand:"x", product=>"x", sales_unit=>a1+a2, is_brand_summary=>true]
// [brand:"x", product=>"x1", sales_unit=>a1, is_brand_summary=>false]
// [brand:"x", product=>"x2", sales_unit=>a2, is_brand_summary=>false]

$exampleResult = [
    // Brand X Summary Record
    [
        'brand' => 'Brand X',
        'product' => 'Brand X',           // Brand name as product name
        'sales_unit' => 150,              // a1 + a2 (100 + 50)
        'sales_value' => 2500.0,          // Aggregated sales value
        'target_unit' => 120,             // Aggregated target
        'target_value' => 2000.0,         // Aggregated target value
        'achievement_unit' => '125.00%',  // Recalculated achievement
        'achievement_value' => '125.00%', // Recalculated achievement
        'p_w' => 25.5,                    // Aggregated product weight
        'product_value' => 300.0,         // Aggregated product value
        'is_brand_summary' => true,       // ✓ Brand summary flag
        'is_total' => false,
        'product_count' => 2,             // Number of products in this brand
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // Individual Product X1
    [
        'brand' => 'Brand X',
        'product' => 'Product X1',        // Original product name
        'sales_unit' => 100,              // a1
        'sales_value' => 1500.0,          // Original sales value
        'target_unit' => 80,              // Original target
        'target_value' => 1200.0,         // Original target value
        'achievement_unit' => '125.00%',  // Original achievement
        'achievement_value' => '125.00%', // Original achievement
        'p_w' => 15.0,                    // Original product weight
        'product_value' => 180.0,         // Original product value
        'is_brand_summary' => false,      // ✓ Individual product flag
        'is_total' => false,
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // Individual Product X2
    [
        'brand' => 'Brand X',
        'product' => 'Product X2',        // Original product name
        'sales_unit' => 50,               // a2
        'sales_value' => 1000.0,          // Original sales value
        'target_unit' => 40,              // Original target
        'target_value' => 800.0,          // Original target value
        'achievement_unit' => '125.00%',  // Original achievement
        'achievement_value' => '125.00%', // Original achievement
        'p_w' => 10.5,                    // Original product weight
        'product_value' => 120.0,         // Original product value
        'is_brand_summary' => false,      // ✓ Individual product flag
        'is_total' => false,
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // Brand Y Summary Record (single product brand)
    [
        'brand' => 'Brand Y',
        'product' => 'Brand Y',           // Brand name as product name
        'sales_unit' => 75,               // Single product value
        'sales_value' => 1125.0,          // Single product value
        'target_unit' => 70,              // Single product value
        'target_value' => 1050.0,         // Single product value
        'achievement_unit' => '107.14%',  // Single product achievement
        'achievement_value' => '107.14%', // Single product achievement
        'p_w' => 18.5,                    // Single product weight
        'product_value' => 150.0,         // Single product value
        'is_brand_summary' => true,       // ✓ Brand summary flag
        'is_total' => false,
        'product_count' => 1,             // Only one product in this brand
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // Individual Product Y1 (only product in Brand Y)
    [
        'brand' => 'Brand Y',
        'product' => 'Product Y1',        // Original product name
        'sales_unit' => 75,               // Original value
        'sales_value' => 1125.0,          // Original sales value
        'target_unit' => 70,              // Original target
        'target_value' => 1050.0,         // Original target value
        'achievement_unit' => '107.14%',  // Original achievement
        'achievement_value' => '107.14%', // Original achievement
        'p_w' => 18.5,                    // Original product weight
        'product_value' => 150.0,         // Original product value
        'is_brand_summary' => false,      // ✓ Individual product flag
        'is_total' => false,
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // No Brand Summary Record
    [
        'brand' => '',                    // Empty for no brand
        'product' => 'No Brand',          // Special label for no brand
        'sales_unit' => 40,               // Products without brand
        'sales_value' => 600.0,           // Products without brand
        'target_unit' => 45,              // Products without brand
        'target_value' => 675.0,          // Products without brand
        'achievement_unit' => '88.89%',   // Calculated achievement
        'achievement_value' => '88.89%',  // Calculated achievement
        'p_w' => 8.0,                     // Products without brand
        'product_value' => 60.0,          // Products without brand
        'is_brand_summary' => true,       // ✓ Brand summary flag
        'is_total' => false,
        'product_count' => 1,             // Products without brand
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // Individual Product with No Brand
    [
        'brand' => '',                    // Empty for no brand
        'product' => 'Generic Product',   // Original product name
        'sales_unit' => 40,               // Original value
        'sales_value' => 600.0,           // Original sales value
        'target_unit' => 45,              // Original target
        'target_value' => 675.0,          // Original target value
        'achievement_unit' => '88.89%',   // Original achievement
        'achievement_value' => '88.89%',  // Original achievement
        'p_w' => 8.0,                     // Original product weight
        'product_value' => 60.0,          // Original product value
        'is_brand_summary' => false,      // ✓ Individual product flag
        'is_total' => false,
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
    
    // Total Record (unchanged)
    [
        'brand' => '',
        'product' => 'Total',
        'sales_unit' => 265,              // Sum of all products
        'sales_value' => 4225.0,          // Sum of all sales values
        'target_unit' => 235,             // Sum of all targets
        'target_value' => 3725.0,         // Sum of all target values
        'achievement_unit' => '112.77%',  // Overall achievement
        'achievement_value' => '113.42%', // Overall achievement
        'p_w' => '',                      // Not applicable for total
        'product_value' => 610.0,         // Sum of all product values
        'is_brand_summary' => false,      // ✓ Not a brand summary
        'is_total' => true,               // ✓ Total record flag
        'employee' => 'John Doe',
        'division' => 'District A',
        // ... other fields
    ],
];

/**
 * Key Points:
 * 
 * 1. STRUCTURE: Each brand group starts with a summary record, followed by individual products
 * 2. IDENTIFICATION: Use 'is_brand_summary' to distinguish summary vs individual records
 * 3. AGGREGATION: Brand summaries contain sum of all products in that brand
 * 4. PRESERVATION: All individual product records are preserved with original data
 * 5. NO BRAND: Products without brands are grouped under "No Brand" with empty brand field
 * 6. TOTAL: Total record remains unchanged at the end
 * 
 * Usage:
 * foreach ($result as $record) {
 *     if ($record['is_brand_summary']) {
 *         echo "Brand Summary: " . $record['product'] . " (Total: " . $record['sales_unit'] . ")\n";
 *     } elseif ($record['is_total']) {
 *         echo "Overall Total: " . $record['sales_unit'] . "\n";
 *     } else {
 *         echo "  - Product: " . $record['product'] . " (" . $record['sales_unit'] . ")\n";
 *     }
 * }
 */
