<?php

namespace App;

use App\Traits\ExcelImportable;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Brand extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    use HasRelationships;

    protected $guard_name = 'api';

    protected $table = 'brands';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'notes',
        'sort',
        'file_id'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function products($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();

        return $this->belongsToMany(Product::class, 'product_brands', 'brand_id', 'product_id')
            ->withPivot(['from_date', 'to_date', 'deleted_at'])
            ->where('product_brands.from_date', '<=', $from)
            ->whereNull('product_brands.deleted_at')
            ->where(fn($q) => $q->where('product_brands.to_date', '=', null)->orWhere('product_brands.to_date', '>=', $to));
    }

    public function product()
    {
        return $this->products()->orderBy("id")->limit(1);
    }
    public function productbrands()
    {
        return $this->hasMany(ProductBrands::class, 'product_brands');
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }
}
