<?php

namespace App;

use App\Helpers\SimpleDate;
use App\Http\Controllers\Product\Message;
use App\Models\Budget;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Distributors\ProductMapping;
use App\Models\EDetailing\Presentation;
use App\Models\Expenses\Expense;
use App\Models\Material;
use App\Models\Product\ProductCeiling;
use App\Models\Product\ProductMessage;
use App\Models\ProductSample;
use App\Models\ProductWeight;
use App\Models\Quiz;
use App\Models\QuizQuestion;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Illuminate\Database\Eloquent\Builder;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Product extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    use HasRelationships;

    public const HIDDEN = '1';
    public const VISIBLE = '0';

    protected $guard_name = 'api';

    protected $table = 'products';

    protected $appends = ['maxSort'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'ucode',
        'name',
        'short_name',
        'classification_id',
        'type_id',
        'invisible',
        'family_id',
        'quantity',
        'notes',
        'sort',
        'launch_date',
        'is_hidden',
        'max_sample',
        'file_id'
    ];


    protected $casts = [
        'launch_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function productCeilings(): HasMany
    {
        return $this->hasMany(ProductCeiling::class, 'product_id')
            ->where('product_ceilings.from_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('product_ceilings.to_date', '>', (string) Carbon::now())
                ->orWhere('product_ceilings.to_date', null));
    }

    public function productCeiling(): HasOne
    {
        return $this->hasOne(ProductCeiling::class, 'product_id');
    }

    protected $hidden = ['pivot'];

    public function classification()
    {
        return $this->belongsTo(Classification::class);
    }

    public function type()
    {
        return $this->belongsTo(Producttype::class);
    }

    public function samples($from = null, $to = null)
    {
        return $this->hasMany(ProductSample::class, 'product_id');
    }

    public function commercials()
    {
        return $this->hasMany(CommercialRequest::class, 'id');
    }

    public function materials()
    {
        return $this->belongsToMany(Material::class, 'material_product');
    }


    public function productbrands()
    {
        return $this->hasMany(ProductBrands::class)->whereNull('product_brands.deleted_at')
            ->where("product_brands.from_date", "<=", now())
            ->where(fn($q) => $q->where('product_brands.to_date', '>', (string)Carbon::now())
                ->orWhere('product_brands.to_date', null));
    }

    public function budget($year = null)
    {
        return $this->hasMany(Budget::class, 'product_id');
    }

    public function brands($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();

        return $this->belongsToMany(Brand::class, 'product_brands')
            ->withPivot(['from_date', 'to_date', 'deleted_at'])
            ->whereNull('product_brands.deleted_at')
            ->where('product_brands.from_date', '<=', $from)
            ->where(fn($q) => $q->where('product_brands.to_date', '>=', $to)
                ->orWhere('product_brands.to_date', null));
    }
    public function brand()
    {
        return $this->brands()->first();
    }
    public function productprices()
    {
        return $this->hasMany(Productprice::class)->whereNull('product_prices.deleted_at')
            ->where('product_prices.from_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('product_prices.to_date', '>', (string) Carbon::now())
                ->orWhere('product_prices.to_date', null));
    }

    public function quizQuestion()
    {
        return $this->belongsToMany(QuizQuestion::class, 'question_product');
    }

    public function productingrediants()
    {
        return $this->hasMany(ProductIngrediants::class);
    }


    public function family()
    {
        return $this->belongsTo(Family::class);
    }

    public function productmanufacturers()
    {
        return $this->hasMany(ProductManufacturers::class);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function lineproducts($from = null, $to = null)
    {
        return $this->hasMany(LineProduct::class)->whereNull('line_products.deleted_at')
            ->where('line_products.from_date', '<=', $from?->toDateString() ?? now())
            ->where(fn($q) => $q->where('line_products.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                ->orWhere('line_products.to_date', null));
    }
    public function allProducts()
    {
        return $this->hasMany(LineProduct::class)->whereNull('line_products.deleted_at');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function lineDivisions()
    {
        return $this->hasManyDeepFromRelations(
            $this->lines()->where('line_products.from_date', '<=', now()),
            (new Line())->divisions()
        )->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
            ->orWhere('line_products.to_date', null));
    }

    public function lineBricks()
    {
        return $this->hasManyDeepFromRelations(
            $this->lines()->where("line_products.from_date", "<=", now()),
            (new Line())->lineBricks()
        )->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
            ->orWhere('line_products.to_date', null));
    }

    public function lineBricksInDates(Carbon|null $from_date = null, Carbon|null $to_date = null)
    {
        $fromDate = $from_date ?: now();
        $toDate = $to_date ?: now();
        return $this->hasManyDeepFromRelations(
            $this->allLines()
                ->where('line_products.from_date', '<=', $fromDate)
                ->where(fn($q) => $q->where('line_products.to_date', '>', $toDate)
                    ->orWhere('line_products.to_date', null)),
            (new Line())->lineBricksInDates($fromDate, $toDate)
        )->where(fn($q) => $q->where('line_products.to_date', '>', $toDate)
            ->orWhere('line_products.to_date', null));
    }

    public function productSpecialities()
    {
        return $this->hasMany(ProductSpecialities::class);
    }

    public static function getMaxSortAttribute()
    {
        return Product::withTrashed()->max('sort');
    }

    public function messages()
    {
        return $this->belongsToMany(ProductMessage::class, 'product_with_messages', 'product_id', 'message_id')->withTimestamps()->withPivot(['deleted_at']);
    }

    public function presentations()
    {
        return $this->belongsToMany(Presentation::class, 'product_presentations', 'product_id', 'presentation_id')->withTimestamps()->withPivot(['deleted_at']);
    }

    public function lines()
    {
        return $this->belongsToMany(Line::class, 'line_products', 'product_id', 'line_id')->withTimestamps()
            ->withPivot(['from_date', 'to_date', 'deleted_at', 'ratio'])
            ->where('line_products.from_date', '<=', now())
            ->whereNull('line_products.deleted_at')
            ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                ->orWhere('line_products.to_date', null));
    }

    public function allLines()
    {
        return $this->belongsToMany(Line::class, 'line_products', 'product_id', 'line_id')->withTimestamps()
            ->withPivot(['from_date', 'to_date', 'deleted_at', 'ratio'])
            ->whereNull('line_products.deleted_at');
    }

    public function distributors()
    {
        return $this->belongsToMany(Distributor::class, 'product_distributors')
            ->using(ProductDistributor::class)->withTimestamps()->withPivot(['code', 'from_date', 'to_date', "deleted_at"])
            ->whereNull("product_distributors.deleted_at");
    }

    public function productDistributors()
    {
        return $this->hasMany(ProductMapping::class);
    }

    public function productsOfOffer()
    {
        return $this->belongsToMany(Product::class, 'product_related_to', 'offer_id', 'product_id')->as('product')->withTimestamps()->withPivot(['units', 'ratios']);
    }

    public function quizzes()
    {
        return $this->belongsToMany(Quiz::class, 'quiz_product');
    }

    public function offer()
    {
        return $this->belongsToMany(Product::class, 'product_related_to', 'product_id', 'offer_id')->as('offer')->withTimestamps();
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function saleDetails(): HasManyThrough
    {
        return $this->hasManyThrough(SaleDetail::class, Sale::class);
    }

    public function salesMappings(): HasManyThrough
    {
        return $this->hasManyThrough(Mapping::class, Sale::class);
    }

    /**
     * @param Carbon $first
     * @param Carbon $last
     * @description the default is using first date of month and last date of month
     */
    public static function salesBetweenTwoDates(Carbon $firstDate, Carbon $lastDate)
    {
        return static::whereHas('sales')->with(['sales' => function ($query) use ($firstDate, $lastDate) {
            $query->orderBy('date')->whereBetween('date', [$firstDate->toDateString(), $lastDate->toDateString()]);
        }]);
    }

    public function actualVisitProduct(): HasMany
    {
        return $this->hasMany(ActualVisitProduct::class, 'product_id');
    }

    public function actualVisit()
    {
        return $this->hasManyThrough(ActualVisit::class, ActualVisitProduct::class, 'product_id', 'id', 'id', 'visit_id');
    }

    public static function salesInMonth($month = null)
    {
        [$first, $last] = SimpleDate::firstAndLastOFMonth($month);
        return static::salesBetweenTwoDates($first, $last);
    }

    public static function salesInYear($year = null)
    {
        [$first, $last] = SimpleDate::firstAndLastOFYear($year);
        return static::salesBetweenTwoDates($first, $last);
    }


    public function setVisibility(bool $visibility)
    {
        $this->invisible = $visibility;
        return $this;
    }

    public function getExpenseProducts()
    {
        return $this->belongsToMany(Expense::class, 'expense_products');
    }

    public function hide()
    {
        $this->invisible = Product::HIDDEN;
        return $this;
    }

    public function unHide()
    {
        $this->invisible = Product::VISIBLE;
        return $this;
    }

    public function isHidden()
    {
        return $this->invisible == Product::HIDDEN;
    }

    public function isVisible()
    {
        return $this->invisible == Product::VISIBLE;
    }

    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        ProductBrands::withTrashed()->where('product_id', $this->id)->restore();
        ProductManufacturers::withTrashed()->where('product_id', $this->id)->restore();
        Productprice::withTrashed()->where('product_id', $this->id)->restore();
        ProductSpecialities::withTrashed()->where('product_id', $this->id)->restore();
    }

    public function forceDelete()
    {
        ProductBrands::withTrashed()->where('product_id', $this->id)->forceDelete();
        ProductManufacturers::withTrashed()->where('product_id', $this->id)->forceDelete();
        Productprice::withTrashed()->where('product_id', $this->id)->forceDelete();
        ProductSpecialities::withTrashed()->where('product_id', $this->id)->forceDelete();
        $this->withTrashed()->where('id', $this->id)->forceDelete();
    }

    public function weights(): HasMany
    {
        return $this->hasMany(ProductWeight::class);
    }
}
