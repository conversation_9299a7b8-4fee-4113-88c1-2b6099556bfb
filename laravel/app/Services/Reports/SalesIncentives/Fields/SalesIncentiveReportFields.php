<?php

namespace App\Services\Reports\SalesIncentives\Fields;

use App\Services\Reports\SalesIncentives\IncentiveReportTypes;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

class SalesIncentiveReportFields
{

    public function getFields(IncentiveReportTypes $view, CarbonPeriod $period, bool $isUserFilter, bool $isBrickChecked)
    {
        // Define base fields based on filters
        $baseFields = $this->getBaseFields(
            $isUserFilter,
            $isBrickChecked
        );

        // Add view-specific fields
        $viewFields = match ($view) {
            IncentiveReportTypes::SALES_VIEW => $this->getSalesViewFields(),
            IncentiveReportTypes::MONTHS_VIEW => $this->getPeriodViewFields($period)
        };

        $incentiveCalculationColumns = [
            "product_value",
            "cov", "freq", "call_r", "kpis_value",
            'coaching_ratio',
            'covered_coaching',
            'vacant_ratio',
            "manager_coverage",
            "m_k_ratio",
            "total_incentive", "75%", "25%"
        ];

        return $baseFields->merge([...$viewFields, ...$incentiveCalculationColumns]);
    }

    /**
     * Get the base fields depending on filters
     *
     * @return \Illuminate\Support\Collection
     */
    private function getBaseFields($isUserFilter, $isBrickChecked): Collection
    {
        // Common fields shared across all conditions
        $commonFields = ["line", "division", 'in_charge_date', "employee", "emp_code", "product", "p_w","brand"];

        // Additional fields that may be included based on conditions
        // $brandField = ["brand"];
        $brickField = ["brick"];


        // if (!$isUserFilter) {
        //     return collect(array_merge($commonFields, $brandField));
        // }

        if (!$isBrickChecked) {
            return collect($commonFields);
        }

        return collect(array_merge($commonFields, $brickField));

    }

    /**
     * Get fields specific to the Sales view
     *
     * @return array
     */
    private function getSalesViewFields(): array
    {
        return [
            "sales_unit", "sales_value", "target_unit", "target_value",
            "achievement_unit", "achievement_value"
        ];
    }

    /**
     * Get fields for period view with monthly metrics
     *
     * @return array
     */
    private function getPeriodViewFields(CarbonPeriod $period): array
    {
        $fields = [];

        // Add monthly metrics for each period
        foreach ($period as $date) {
            $month = $date->format('M');
            $fields = array_merge($fields, [
                "$month SU", "$month SV",
                "$month TU", "$month TV",
                "$month AU", "$month AV"
            ]);
        }

        // Add totals and achievements
        $totals = [
            'Tot SU', 'Tot SV', 'Tot TU', 'Tot TV', 'Ach U', 'Ach V'
        ];

        return array_merge($fields, $totals);
    }
}
